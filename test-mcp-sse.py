#!/usr/bin/env python3
"""
MCP SSE Protocol Test Script
Tests the MCP Server-Sent Events implementation to ensure proper protocol compliance
"""

import requests
import json
import time
import uuid

BASE_URL = "http://*************:8000"

def test_mcp_sse_connection():
    """Test MCP SSE connection and protocol"""
    print("🌊 Testing MCP SSE Connection...")
    
    try:
        url = f"{BASE_URL}/mcp/sse"
        print(f"Connecting to: {url}")
        
        response = requests.get(url, stream=True, timeout=15)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ MCP SSE Connection Successful!")
            print("📡 Reading MCP SSE events...")
            
            events_received = []
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"SSE: {line}")
                    events_received.append(line)
                    if len(events_received) >= 10:  # Read first 10 events
                        break
            
            # Check for required MCP events
            has_endpoint = any("endpoint" in line for line in events_received)
            has_capabilities = any("capabilities" in line for line in events_received)
            
            if has_endpoint and has_capabilities:
                print("✅ MCP Protocol events detected!")
                return True
            else:
                print("⚠️ Missing required MCP protocol events")
                return False
        else:
            print(f"❌ MCP SSE Connection Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP SSE Error: {str(e)}")
        return False

def test_mcp_initialize():
    """Test MCP initialization"""
    print("\n🔧 Testing MCP Initialize...")
    
    try:
        url = f"{BASE_URL}/mcp/messages/"
        message = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        response = requests.post(url, json=message, timeout=30)
        
        if response.status_code == 200:
            print("✅ MCP Initialize Successful!")
            result = response.json()
            print(f"Result: {json.dumps(result, indent=2)}")
            
            # Check for required fields
            if "result" in result and "serverInfo" in result["result"]:
                print("✅ Valid MCP initialize response!")
                return True
            else:
                print("⚠️ Invalid MCP initialize response format")
                return False
        else:
            print(f"❌ MCP Initialize Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP Initialize Error: {str(e)}")
        return False

def test_mcp_tools_list():
    """Test MCP tools/list"""
    print("\n📋 Testing MCP Tools List...")
    
    try:
        url = f"{BASE_URL}/mcp/messages/"
        message = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "tools/list",
            "params": {}
        }
        
        response = requests.post(url, json=message, timeout=30)
        
        if response.status_code == 200:
            print("✅ MCP Tools List Successful!")
            result = response.json()
            print(f"Result: {json.dumps(result, indent=2)}")
            
            # Check for tools
            if "result" in result and "tools" in result["result"]:
                tools = result["result"]["tools"]
                tool_names = [tool["name"] for tool in tools]
                print(f"Available tools: {tool_names}")
                
                expected_tools = ["verify_pan_basic", "verify_pan_comprehensive", "verify_pan_kra"]
                if all(tool in tool_names for tool in expected_tools):
                    print("✅ All expected tools found!")
                    return True
                else:
                    print("⚠️ Some expected tools missing")
                    return False
            else:
                print("⚠️ Invalid tools list response format")
                return False
        else:
            print(f"❌ MCP Tools List Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP Tools List Error: {str(e)}")
        return False

def test_mcp_tool_call():
    """Test MCP tool call"""
    print("\n🔧 Testing MCP Tool Call...")
    
    try:
        url = f"{BASE_URL}/mcp/messages/"
        message = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "tools/call",
            "params": {
                "name": "verify_pan_comprehensive",
                "arguments": {
                    "id_number": "**********"
                }
            }
        }
        
        response = requests.post(url, json=message, timeout=30)
        
        if response.status_code == 200:
            print("✅ MCP Tool Call Successful!")
            result = response.json()
            print(f"Result: {json.dumps(result, indent=2)}")
            
            # Check for valid response
            if "result" in result and "content" in result["result"]:
                print("✅ Valid MCP tool call response!")
                return True
            else:
                print("⚠️ Invalid tool call response format")
                return False
        else:
            print(f"❌ MCP Tool Call Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP Tool Call Error: {str(e)}")
        return False

def test_mcp_info():
    """Test MCP info endpoint"""
    print("\n📋 Testing MCP Info...")
    
    try:
        url = f"{BASE_URL}/mcp/sse/info"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✅ MCP Info Successful!")
            data = response.json()
            print(f"Info: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ MCP Info Failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ MCP Info Error: {str(e)}")
        return False

def main():
    """Run all MCP SSE tests"""
    print("🚀 Starting MCP SSE Protocol Tests")
    print(f"Base URL: {BASE_URL}")
    
    tests = [
        ("MCP Info", test_mcp_info),
        ("MCP SSE Connection", test_mcp_sse_connection),
        ("MCP Initialize", test_mcp_initialize),
        ("MCP Tools List", test_mcp_tools_list),
        ("MCP Tool Call", test_mcp_tool_call),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        success = test_func()
        if success:
            passed += 1
        
        time.sleep(2)  # Delay between tests
    
    print(f"\n{'='*60}")
    print(f"📊 MCP SSE Test Results: {passed}/{total} tests passed")
    print('='*60)
    
    if passed == total:
        print("🎉 All MCP SSE tests passed! Ready for n8n MCP Client integration!")
        return 0
    else:
        print("❌ Some MCP SSE tests failed. Check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
