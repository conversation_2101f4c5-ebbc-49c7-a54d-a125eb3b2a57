{"name": "KYC PAN Verification Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "verify-pan", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "kyc-pan-webhook"}, {"parameters": {"url": "http://kyc-server:8000/api/verify/pan/comprehensive", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "id_number", "value": "={{ $json.pan_number }}"}]}, "options": {"response": {"response": {"responseFormat": "json"}}}}, "id": "kyc-api-call", "name": "KYC API Call", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "KYC API Call", "type": "main", "index": 0}]]}, "KYC API Call": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}