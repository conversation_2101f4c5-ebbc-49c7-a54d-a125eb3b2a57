"""
SSE Transport Layer for KYC MCP Server
This module provides proper MCP protocol implementation over Server-Sent Events
to enable integration with n8n MCP client nodes.
"""

import logging
import json
import asyncio
import uuid
from typing import Dict, Any, Optional
from sse_starlette import EventSourceResponse
from starlette.applications import Starlette
from starlette.routing import Route
from starlette.responses import JSONResponse
from starlette.requests import Request

logger = logging.getLogger("kyc-sse-transport")

def create_sse_server(mcp_server=None) -> Starlette:
    """
    Create a Starlette app that implements proper MCP protocol over SSE
    for n8n MCP client node integration.

    Returns:
        Starlette application configured for MCP SSE transport
    """

    # Store active sessions
    active_sessions = {}

    async def handle_sse(request):
        """Handle SSE connections with proper MCP protocol"""
        try:
            # Generate session ID
            session_id = str(uuid.uuid4())
            logger.info(f"New MCP SSE connection established: {session_id}")

            async def mcp_event_generator():
                try:
                    # Send MCP initialization according to protocol
                    yield {
                        "event": "endpoint",
                        "data": f"/messages/?session_id={session_id}"
                    }

                    # Send server info
                    yield {
                        "event": "server_info",
                        "data": json.dumps({
                            "name": "KYC Verification Server",
                            "version": "1.0.0",
                            "protocol_version": "2024-11-05"
                        })
                    }

                    # Send capabilities
                    yield {
                        "event": "capabilities",
                        "data": json.dumps({
                            "tools": {
                                "verify_pan_basic": {
                                    "description": "Basic PAN verification",
                                    "inputSchema": {
                                        "type": "object",
                                        "properties": {
                                            "id_number": {"type": "string", "description": "PAN number"}
                                        },
                                        "required": ["id_number"]
                                    }
                                },
                                "verify_pan_comprehensive": {
                                    "description": "Comprehensive PAN verification with address",
                                    "inputSchema": {
                                        "type": "object",
                                        "properties": {
                                            "id_number": {"type": "string", "description": "PAN number"}
                                        },
                                        "required": ["id_number"]
                                    }
                                },
                                "verify_pan_kra": {
                                    "description": "PAN verification using KRA database",
                                    "inputSchema": {
                                        "type": "object",
                                        "properties": {
                                            "id_number": {"type": "string", "description": "PAN number"}
                                        },
                                        "required": ["id_number"]
                                    }
                                }
                            },
                            "resources": {
                                "kyc://api/status": {
                                    "description": "API status and connectivity"
                                },
                                "kyc://api/endpoints": {
                                    "description": "Available API endpoints"
                                }
                            }
                        })
                    }

                    # Store session
                    active_sessions[session_id] = {
                        "created": asyncio.get_event_loop().time(),
                        "active": True
                    }

                    # Keep connection alive with pings
                    ping_counter = 0
                    while active_sessions.get(session_id, {}).get("active", False):
                        await asyncio.sleep(30)
                        ping_counter += 1
                        yield {
                            "event": "ping",
                            "data": f"ping-{ping_counter}"
                        }

                except Exception as e:
                    logger.error(f"Error in MCP event generator: {str(e)}")
                    yield {
                        "event": "error",
                        "data": json.dumps({"error": str(e)})
                    }
                finally:
                    # Clean up session
                    if session_id in active_sessions:
                        active_sessions[session_id]["active"] = False
                        del active_sessions[session_id]
                    logger.info(f"MCP SSE session closed: {session_id}")

            return EventSourceResponse(mcp_event_generator())

        except Exception as e:
            logger.error(f"Error establishing MCP SSE connection: {str(e)}")
            return JSONResponse(
                {"error": f"MCP SSE connection failed: {str(e)}"},
                status_code=500
            )

    async def handle_sse_info(request):
        """Provide information about the SSE endpoint"""
        return JSONResponse({
            "service": "KYC MCP Server",
            "transport": "Server-Sent Events",
            "endpoint": "/sse",
            "status": "ready",
            "mcp_version": "1.0.0",
            "available_tools": [
                "verify_pan_basic",
                "verify_pan_comprehensive",
                "verify_pan_kra"
            ],
            "available_resources": [
                "kyc://api/status",
                "kyc://api/endpoints"
            ]
        })

    async def handle_mcp_messages(request):
        """Handle MCP protocol messages via POST"""
        try:
            body = await request.json()
            logger.info(f"Received MCP message: {json.dumps(body, indent=2)}")

            # Extract MCP message components
            message_id = body.get("id")
            method = body.get("method")
            params = body.get("params", {})

            # Handle different MCP methods
            if method == "tools/call":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})

                logger.info(f"MCP tool call: {tool_name} with arguments: {arguments}")

                # Route to appropriate tool
                if tool_name == "verify_pan_basic":
                    from kyc_mcp_sse import verify_pan_basic_direct
                    result = await verify_pan_basic_direct(arguments.get("id_number"))
                elif tool_name == "verify_pan_comprehensive":
                    from kyc_mcp_sse import verify_pan_comprehensive_direct
                    result = await verify_pan_comprehensive_direct(arguments.get("id_number"))
                elif tool_name == "verify_pan_kra":
                    from kyc_mcp_sse import verify_pan_kra_direct
                    result = await verify_pan_kra_direct(arguments.get("id_number"))
                else:
                    return JSONResponse({
                        "jsonrpc": "2.0",
                        "id": message_id,
                        "error": {
                            "code": -32601,
                            "message": f"Unknown tool: {tool_name}"
                        }
                    })

                # Return MCP response
                return JSONResponse({
                    "jsonrpc": "2.0",
                    "id": message_id,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": result
                            }
                        ]
                    }
                })

            elif method == "tools/list":
                # Return available tools
                return JSONResponse({
                    "jsonrpc": "2.0",
                    "id": message_id,
                    "result": {
                        "tools": [
                            {
                                "name": "verify_pan_basic",
                                "description": "Basic PAN verification",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "id_number": {"type": "string", "description": "PAN number"}
                                    },
                                    "required": ["id_number"]
                                }
                            },
                            {
                                "name": "verify_pan_comprehensive",
                                "description": "Comprehensive PAN verification with address",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "id_number": {"type": "string", "description": "PAN number"}
                                    },
                                    "required": ["id_number"]
                                }
                            },
                            {
                                "name": "verify_pan_kra",
                                "description": "PAN verification using KRA database",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "id_number": {"type": "string", "description": "PAN number"}
                                    },
                                    "required": ["id_number"]
                                }
                            }
                        ]
                    }
                })

            elif method == "initialize":
                # Handle MCP initialization
                return JSONResponse({
                    "jsonrpc": "2.0",
                    "id": message_id,
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {},
                            "resources": {}
                        },
                        "serverInfo": {
                            "name": "KYC Verification Server",
                            "version": "1.0.0"
                        }
                    }
                })

            else:
                return JSONResponse({
                    "jsonrpc": "2.0",
                    "id": message_id,
                    "error": {
                        "code": -32601,
                        "message": f"Unknown method: {method}"
                    }
                })

        except Exception as e:
            logger.error(f"Error in MCP message handling: {str(e)}")
            return JSONResponse({
                "jsonrpc": "2.0",
                "id": body.get("id") if isinstance(body, dict) else None,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }, status_code=500)

    # Create Starlette routes for SSE and message handling
    routes = [
        Route("/sse", endpoint=handle_sse, methods=["GET"]),
        Route("/sse/info", endpoint=handle_sse_info, methods=["GET"]),
        Route("/messages/", endpoint=handle_mcp_messages, methods=["POST"]),
        Route("/call", endpoint=handle_mcp_messages, methods=["POST"]),  # Legacy endpoint
    ]

    # Create a Starlette app
    sse_app = Starlette(routes=routes)
    logger.info("SSE transport layer initialized")

    return sse_app
