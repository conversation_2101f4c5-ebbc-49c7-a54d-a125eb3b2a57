{"name": "KYC MCP SSE Verification Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "verify-pan-mcp", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "kyc-mcp-webhook"}, {"parameters": {"url": "http://139.59.70.153:8000/mcp/call", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "verify_pan_comprehensive"}, {"name": "parameters", "value": "={{ {\"id_number\": $json.pan_number} }}"}]}, "options": {"response": {"response": {"responseFormat": "json"}}}}, "id": "mcp-call", "name": "KYC MCP Call", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.result ? JSON.parse($json.result) : $json }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "KYC MCP Call", "type": "main", "index": 0}]]}, "KYC MCP Call": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "2"}