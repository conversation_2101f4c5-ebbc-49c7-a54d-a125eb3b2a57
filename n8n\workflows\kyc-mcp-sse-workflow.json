{"name": "KYC MCP SSE Verification Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "verify-pan-mcp-sse", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "kyc-mcp-sse-webhook"}, {"parameters": {"url": "http://139.59.70.153:8000/mcp/messages/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "bodyParametersJson": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": \"{{ $runIndex }}-{{ $now }}\",\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"verify_pan_comprehensive\",\n    \"arguments\": {\n      \"id_number\": \"{{ $json.pan_number }}\"\n    }\n  }\n}", "options": {"response": {"response": {"responseFormat": "json"}}}}, "id": "mcp-call", "name": "KYC MCP Protocol Call", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json.result && $json.result.content && $json.result.content[0] ? JSON.parse($json.result.content[0].text) : $json }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "KYC MCP Protocol Call", "type": "main", "index": 0}]]}, "KYC MCP Protocol Call": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "2"}